"use client";
import type React from "react";
import type { HTMLAttributes } from "react";
import { Search, Plus, SquarePen } from "lucide-react";
import { useState, useEffect } from "react"; // Import useEffect
import { usePathname, useRouter } from "next/navigation";
import Image from "next/image";
import { useAuth } from "@/context/AuthContext";

import {
  BarChart3,
  FileText,
  LayoutDashboard,
  Menu,
  Settings,
  Package2,
  Users,
  User,
  LogOut,
  Group,
  Building,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import Cookies from "js-cookie";
import apiClient from "@/lib/apiClient";
import jwt from "jsonwebtoken";

// const user = {
//   profilePic: "/images/leomin.jpeg", // or "" if not uploaded
// };

interface TemplateProps {
  children: React.ReactNode;
}

export default function Template({ children }: TemplateProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const { logout } = useAuth();
  const [user, setUser] = useState<any>({});

  const accessToken = Cookies.get("accessToken");
  const decodedToken = jwt.decode(accessToken) as any;
  const user_id = decodedToken?.user_id;

  const fetchUser = async () => {
    try {
      const response = await apiClient.get(`/user/?id=${user_id}`);
      setUser(response.data);
    } catch (error) {
      console.error("Failed to fetch user:", error);
    }
  };

  useEffect(() => {
    fetchUser();
  }, []);
  // console.log(user[0]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await logout();
      console.log("Logout successful");
      router.push("/");
    } catch (err: any) {
      console.error("Logout failed", err);
    }
  };

  // Add a new state for window width
  const [windowWidth, setWindowWidth] = useState(0);

  // useEffect to handle window resizing

  useEffect(() => {
    // Set initial window width on component mount

    setWindowWidth(window.innerWidth);

    const handleResize = () => {
      setWindowWidth(window.innerWidth);

      // Auto-collapse sidebar if window width is less than a certain threshold (e.g., 768px for md breakpoint)

      if (window.innerWidth < 768) {
        // You can adjust this value

        setIsSidebarCollapsed(true);
      } else {
        setIsSidebarCollapsed(false); // Optionally uncollapse if resized larger
      }
    };

    window.addEventListener("resize", handleResize);

    // Cleanup the event listener on component unmount

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []); 

  return (
    <div className="flex h-screen bg-gray-200">
      {/* Sidebar */}

      <div
        className={`bg-[#0e1e2e] text-white transition-all duration-300 flex flex-col ${
          isSidebarCollapsed ? "w-[50px]" : "w-[170px] overflow-hidden"
        }`}
      >
        <div className="p-2 flex items-center justify-center">
          <div className="relative h-2 w-full flex items-center justify-center mt-3">
            <Image
              src="/images/workalaya-icon.png"
              alt="Workalaya Icon"
              width={30}
              height={30}
              className={`absolute transition-opacity duration-300 ${
                isSidebarCollapsed ? "opacity-100" : "opacity-0"
              }`}
            />

            <Image
              src="/images/workalaya-logo.png"
              alt="Workalaya Logo"
              width={100}
              height={30}
              className={`absolute transition-opacity duration-300 ${
                isSidebarCollapsed ? "opacity-0" : "opacity-100"
              }`}
            />
          </div>
        </div>

        <nav className="mt-3 flex flex-col h-[calc(100vh-80px)]">
          <SidebarItem
            icon={<LayoutDashboard size={20} />}
            text="Dashboard"
            collapsed={isSidebarCollapsed}
            href="/app"
          />

          <SidebarItem
            icon={<Users size={20} />}
            text="Customers"
            collapsed={isSidebarCollapsed}
            href="/app/customers"
          />

          <SidebarItem
            icon={<FileText size={20} />}
            text="Invoices"
            collapsed={isSidebarCollapsed}
            href="/app/invoices"
          />

          <SidebarItem
            icon={<Package2 size={20} />}
            text="Packages"
            collapsed={isSidebarCollapsed}
            href="/app/packages"
          />

          <SidebarItem
            icon={<BarChart3 size={20} />}
            text="Reports"
            collapsed={isSidebarCollapsed}
            href="/app/reports"
          />

          <SidebarItem
            icon={<Settings size={20} />}
            text="Settings"
            active={pathname.startsWith("/app/settings")}
            collapsed={isSidebarCollapsed}
            href="/app/settings"
            onClick={() => setIsSettingsOpen(!isSettingsOpen)}
            ariaExpanded={isSettingsOpen}
          />

          <div
            className={`overflow-hidden transition-all duration-300 ${
              isSettingsOpen ? "max-h-40" : "max-h-0"
            }`}
          >
            <div
              className={`${
                isSidebarCollapsed ? "ml-0" : "ml-2"
              } pl-1 border-l-1 border-[#1a2c3f] space-y-1`}
            >
              <SidebarItem
                icon={<User className="w-6 block" />}
                text="Users"
                active={pathname === "/app/settings/users"}
                collapsed={isSidebarCollapsed}
                href="/app/settings/users"
              />

              <SidebarItem
                icon={<Group className="w-6 block" />}
                text="Groups"
                active={pathname === "/app/settings/groups"}
                collapsed={isSidebarCollapsed}
                href="/app/settings/groups"
              />

              <SidebarItem
                icon={<Building className="w-6 block" />}
                text="Organizations"
                active={pathname === "/app/settings/organizations"}
                collapsed={isSidebarCollapsed}
                href="/app/settings/organizations"
              />
            </div>
          </div>
        </nav>

        <div className="mt-auto">
          <div
            className={`border-t border-gray-800 py-4 px-4 text-xs text-gray-400 transition-opacity duration-300 text-center flex justify-center ${
              isSidebarCollapsed ? "opacity-0" : "opacity-100"
            }`}
          >
            © 2025 Workalaya <br />
            All Rights Reserved
          </div>
        </div>
      </div>

      {/* Main Content */}

      <div className="flex-1 flex flex-col overflow-hidden ">
        {/* Header */}

        <header className="bg-grey-200 shadow-md flex items-center justify-between p-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
            className="mr-2"
          >
            <Menu size={20} />
          </Button>

          <div className="flex-1 mx-0 relative ">
            {" "}
            {/* Added 'relative' for icon positioning */}
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />{" "}
            {/* Search icon */}
            <Input
              type="search"
              placeholder="Search..." // Updated placeholder
              className="max-w-[200px] h-9 bg-gray-50 pl-9 pr-3" // Adjusted pl-9 for icon
            />
          </div>

          <div className="flex items-center gap-2 mr-5">
            <div className="relative group">
              <button onClick={handleSubmit}>
                <div className="w-8 h-8 mr-5 rounded-full bg-gray-200 flex items-center justify-center cursor-pointer hover:bg-gray-300 transition overflow-hidden">
                  <LogOut className="h-4 w-4 text-gray-500" />
                </div>
              </button>

              {/* Tooltip below */}

              <div className="absolute top-full left-1/3 -translate-x-1/2 mt-2 py-1.5 px-3 bg-[#222] rounded text-white text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 flex items-center">
                {/* Tooltip Arrow */}
                <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-[#222] rotate-45"></div>
                Logout
              </div>
            </div>

            <span className="text-sm">
              Hello, {user[0]?.username}!
            </span>

            <Link href="/app/userprofile">
              <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center cursor-pointer hover:bg-gray-400 transition overflow-hidden">
                {user[0]?.profilePic ? (
                  <Image
                    src={user[0].profilePic}
                    alt="Profile Picture"
                    width={32}
                    height={32}
                    className="rounded-full object-cover w-7 h-7"
                  />
                ) : (
                  <User className="h-5 w-5 text-gray-600" />
                )}
              </div>
            </Link>
          </div>
        </header>

        {/* Page Content */}

        <main className="flex-1 overflow-y-auto p-0">{children}</main>
      </div>
    </div>
  );
}

interface SidebarItemProps {
  icon: React.ReactNode;
  text: string;
  active?: boolean;
  collapsed: boolean;
  href: string;

  onClick?: () => void;

  ariaExpanded?: boolean;
}

function SidebarItem({
  icon,
  text,
  active,
  collapsed,
  href,
  onClick,
  ariaExpanded,
}: SidebarItemProps) {
  return (
    <div className="relative group">
      <Link
        href={href}
        onClick={onClick}
        aria-expanded={ariaExpanded}
        group={onClick ? "button" : undefined}
      >
        <div
          className={`flex items-center gap-3 ${
            collapsed ? "justify-center" : "px-4"
          } py-3 cursor-pointer ${
            active ? "bg-[#1a2c3f]" : "hover:bg-[#1a2c3f]"
          }`}
        >
          {icon}

          {!collapsed && <span className="text-sm">{text}</span>}
        </div>
      </Link>

      {/* Tooltip that appears on hover when sidebar is collapsed */}

      {collapsed && (
        <div className="absolute left-full top-1/2 -translate-y-1/2 ml-2 py-1.5 px-3 bg-[#222] rounded text-white text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 flex items-center">
          <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1 w-2 h-2 bg-[#222] rotate-45"></div>

          {text}
        </div>
      )}
    </div>
  );
}
