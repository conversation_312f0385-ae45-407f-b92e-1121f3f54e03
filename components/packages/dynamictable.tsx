"use client";

import { Package, PackageDuration } from "@/app/packages/page";

type DynamicTableProps = {
  tablename: string;
  packages: Package[];
};

export default function DynamicTable({ tablename, packages }: DynamicTableProps) {
    if (!packages) {
    return null;
  }
  const columns = Object.keys(packages[0]);
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border text-sm">
        <thead className="bg-gray-100 text-left">
          <tr>
            {columns.map((column) => (
              <th key={column} className="border px-4 py-2">
                {column}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {packages.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              {columns.map((column) => (
                <td key={column} className="border px-4 py-2">
                  {row[column]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>

  );
}
    