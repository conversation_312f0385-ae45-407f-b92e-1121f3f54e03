"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import apiClient from "@/lib/apiClient";
import Cookies from "js-cookie";
import jwt from "jsonwebtoken";

export default function UserProfile() {
  const [user, setUser] = useState<any>({});

  const accessToken = Cookies.get("accessToken");
  const decodedToken = jwt.decode(accessToken) as any;
  const user_id = decodedToken?.user_id;

  const fetchUser = async () => {
    try {
      const response = await apiClient.get(`/user/?id=${user_id}`);
      setUser(response.data);
    } catch (error) {
      console.error("Failed to fetch user:", error);
    }
  };

  useEffect(() => {
    fetchUser();
  }, []);
  // console.log(user[0]);

  return (
    <div className="p-5">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10 bg-white p-6 rounded-lg shadow-md ">
        {/* Left: Profile Section */}
        <div>
          <h2 className="text-2xl font-bold mb-6">👤 My Profile</h2>

          {/* Profile Picture
          <div className="flex items-center mb-6">
            <Image
              src={user.profilePic}
              alt="Profile"
              width={60}
              height={60}
              className="rounded-md object-cover mr-3"
            />
            <Button variant="secondary" size="sm">
              Change Picture
            </Button>
          </div> */}

          {/* Personal Info */}
          <div className="space-y-3 mr-10">
            {/* <div>
              <label className="block text-sm text-black-700">Name</label>
              <Input value={user.name} disabled />
            </div> */}
            <div>
              <label className="block text-sm text-black-700">Username</label>
              <Input value={user[0]?.username} disabled />
            </div>
            <div>
              <label className="block text-sm text-black-700">Email</label>
              <Input value={user[0]?.email} disabled />
            </div>
            <div>
              <label className="block text-sm text-black-700">
                Group
              </label>
              <Input value={user[0]?.group} disabled />
            </div>
          </div>
        </div>

        {/* Right: Change Password */}
        <div>
          <h2 className="text-2xl font-bold mb-6">🔐 Change Password</h2>
          <div className="space-y-3 mr-10">
            <div>
              <label className="text-sm text-black-700">New Password</label>
              <Input type="password" placeholder="Enter new password" />
            </div>
            <div>
              <label className="text-sm text-black-700">Confirm Password</label>
              <Input type="password" placeholder="Confirm new password" />
            </div>
            <Button
              className="mt-4"
              size="sm"
              onClick={() => alert("Password has been updated!")}
            >
              Update Password
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
