"use client";

import React from "react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import Cookies from "js-cookie";
import apiClient from "@/lib/apiClient";

export interface PackageDuration {
  month: string;
  price: number;
}
export interface Package {
  id: number;
  name: string;
  upload: string;
  download: string;
  type: string;
  durations: PackageDuration[];
}

type DynamicTableProps = {
  tablename: string;
  packages: Package[];
};

export default function PackagesPage() {
  const router = useRouter();
  const [search, setSearch] = useState("");
  const [packagesList, setPackagesList] = useState<Package[]>([]);

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await apiClient.get("/package");
        setPackagesList(response.data.data);
        console.log(response.data.data[0].name);
      } catch (error) {
        console.error("Failed to fetch packages:", error);
      }
    };
    fetchPackages();
  }, []);


  // Flatten packages for table display
  const flattenedPackages = packagesList.flatMap(pkg =>
    pkg.durations.map(duration => ({
      ...pkg,
      duration: duration.month,
      price: duration.price,
    }))
  );

  // Filter data based on search
  const filteredPackages = flattenedPackages.filter((pkg) =>
    pkg?.name.toLowerCase().includes(search.toLowerCase())
  );

  // Delete functions
  const handleDeletePackage = (id: number) => {
    if (window.confirm("Are you sure you want to delete this package?")) {
      setPackagesList(packagesList.filter((pkg) => pkg.id !== id));
    }
  };

  // Edit functions
  const handleEditPackage = (id: number) => {
    router.push(`/app/packages/edit/${id}`);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">12,000</div>
            <div className="text-sm text-gray-500">Total Clients</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">8,000 / 12,000</div>
            <div className="text-sm text-gray-500">100/100Mbps</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">3,000 / 12,000</div>
            <div className="text-sm text-gray-500">200/200Mbps</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">1,000 / 12,000</div>
            <div className="text-sm text-gray-500">300/300Mbps</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-sm">
        {/* Header with tabs and actions */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-2xl font-bold">Package Management</h1>
            <div className="flex gap-2">
              <Button
                onClick={() => router.push("/app/packages/add")}
                className="bg-blue-600 hover:bg-black-700"
              >
                Add Package
              </Button>
            </div>
          </div>

          {/* Tabs and Search */}
          <div className="flex justify-between items-center">
            <input
              type="text"
              placeholder={`Search Packages...`}
              value={search}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setSearch(e.target.value)
              }
              className="w-[300px] p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
            />
          </div>
        </div>

        {/* Package Table */}
        <div className="p-6">
          <div className="overflow-x-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-xl font-bold">{filteredPackages[0]?.name}</h1>
            </div>
            <table className="min-w-full border text-sm">
              <thead className="bg-gray-100 text-left">
                <tr>
                  <th className="border px-4 py-2">SN</th>
                  <th className="border px-4 py-2">Package Name</th>
                  <th className="border px-4 py-2">Duration (Months)</th>
                  <th className="border px-4 py-2">Upload Speed</th>
                  <th className="border px-4 py-2">Download Speed</th>
                  <th className="border px-4 py-2">Price</th>
                  <th className="border px-4 py-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPackages?.map((pkg, index) => (
                  <tr key={`${pkg.id}-${pkg.duration}`} className="hover:bg-gray-50">
                    <td className="border px-4 py-2">{index + 1}</td>
                    <td className="border px-4 py-2 font-medium">{pkg.name}</td>
                    <td className="border px-4 py-2">{pkg.duration}</td>
                    <td className="border px-4 py-2">{pkg.upload}</td>
                    <td className="border px-4 py-2">{pkg.download}</td>
                    <td className="border px-4 py-2 text-green-600 font-medium">{pkg.price}</td>
                    <td className="border px-4 py-2">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditPackage(pkg.id)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                          onClick={() => handleDeletePackage(pkg.id)}
                        >
                          Delete
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {filteredPackages?.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No packages found matching your search.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
