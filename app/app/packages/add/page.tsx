"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import ISPpackagesForm from "@/components/packages/isp-packages-form";
import { ArrowLeft } from "lucide-react";
import apiClient from "@/lib/apiClient";

export default function AddPackagePage() {
  const router = useRouter();
  const handleSubmit = async (packageData: any) => {
    try {
      const response = await apiClient.post("/package", packageData);
      console.log("Package created:", response.data);
      router.push("/app/packages");
    }
    catch (error) {
      console.error("Failed to create package:", error);
      alert("Failed to create package");
      return;
    }
  };

  const handleCancel = () => {
    router.push("/app/packages");
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Packages
        </Button>
        <h1 className="text-2xl font-bold">Add New Package</h1>
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <ISPpackagesForm onSubmit={handleSubmit} onCancel={handleCancel} isEdit={false} />
      </div>
    </div>
  );
}
